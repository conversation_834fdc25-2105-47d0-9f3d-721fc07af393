import { Request, Response } from 'express'
import { StatusCodes } from 'http-status-codes'
import path from 'path'
import { employeeService } from '../services/employeeService'
import { oauthClientService } from '../services/oauthClientService'
import { oauthTokenService } from '../services/oauthTokenService'

// Authorization endpoint for authorization code flow
export const authorize = async (req: Request, res: Response): Promise<void> => {
  try {
    if (req.method === 'GET') {
      // Show authorization page
      await handleAuthorizationPage(req, res)
    } else if (req.method === 'POST') {
      // Process authorization
      await handleAuthorizationSubmission(req, res)
    } else {
      res.status(StatusCodes.METHOD_NOT_ALLOWED).json({
        error: 'method_not_allowed',
        error_description: 'Only GET and POST methods are allowed',
      })
    }
  } catch (error) {
    console.error('Authorization error:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      error_description: 'Internal server error',
    })
  }
}

// Handle GET request - show authorization page
const handleAuthorizationPage = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { response_type, client_id, redirect_uri, state } = req.query

  // Validate required parameters
  if (!response_type || !client_id || !redirect_uri) {
    const errorUrl = new URL((redirect_uri as string) || 'about:blank')
    errorUrl.searchParams.set('error', 'invalid_request')
    errorUrl.searchParams.set(
      'error_description',
      'Missing required parameters'
    )
    if (state) errorUrl.searchParams.set('state', state as string)
    res.redirect(errorUrl.toString())
    return
  }

  if (response_type !== 'code') {
    const errorUrl = new URL(redirect_uri as string)
    errorUrl.searchParams.set('error', 'unsupported_response_type')
    errorUrl.searchParams.set(
      'error_description',
      'Only authorization code flow is supported'
    )
    if (state) errorUrl.searchParams.set('state', state as string)
    res.redirect(errorUrl.toString())
    return
  }

  // Validate client
  const client = await oauthClientService.getClientByClientId(
    client_id as string
  )
  if (!client) {
    const errorUrl = new URL(redirect_uri as string)
    errorUrl.searchParams.set('error', 'invalid_client')
    errorUrl.searchParams.set('error_description', 'Invalid client_id')
    if (state) errorUrl.searchParams.set('state', state as string)
    res.redirect(errorUrl.toString())
    return
  }

  // Validate redirect URI
  const isValidRedirectUri = await oauthClientService.validateRedirectUri(
    client_id as string,
    redirect_uri as string
  )
  if (!isValidRedirectUri) {
    const errorUrl = new URL(redirect_uri as string)
    errorUrl.searchParams.set('error', 'invalid_request')
    errorUrl.searchParams.set('error_description', 'Invalid redirect_uri')
    if (state) errorUrl.searchParams.set('state', state as string)
    res.redirect(errorUrl.toString())
    return
  }

  // Serve the authorization page
  res.sendFile(path.join(__dirname, '../../public/authorize.html'))
}

// Handle POST request - process authorization
const handleAuthorizationSubmission = async (
  req: Request,
  res: Response
): Promise<void> => {
  const {
    response_type,
    client_id,
    redirect_uri,
    scope,
    state,
    username,
    password,
  } = req.body

  // Validate required parameters
  if (!response_type || !client_id || !redirect_uri || !username || !password) {
    const errorUrl = new URL(redirect_uri as string)
    errorUrl.searchParams.set('error', 'invalid_request')
    errorUrl.searchParams.set(
      'error_description',
      'Missing required parameters'
    )
    if (state) errorUrl.searchParams.set('state', state as string)
    res.redirect(errorUrl.toString())
    return
  }

  // Authenticate user
  const employee = await employeeService.validateEmployee(username, password)
  if (!employee) {
    const errorUrl = new URL(redirect_uri as string)
    errorUrl.searchParams.set('error', 'access_denied')
    errorUrl.searchParams.set(
      'error_description',
      'Invalid username or password'
    )
    if (state) errorUrl.searchParams.set('state', state as string)
    res.redirect(errorUrl.toString())
    return
  }

  // Generate authorization code
  const authCode = await oauthTokenService.createAuthorizationCode({
    client_id: client_id as string,
    employee_id: employee.employee_id,
    redirect_uri: redirect_uri as string,
    scope: (scope as string) || 'read',
    code_challenge: undefined,
    code_challenge_method: undefined,
  })

  // Redirect back to client with authorization code
  const redirectUrl = new URL(redirect_uri as string)
  redirectUrl.searchParams.set('code', authCode)
  if (state) {
    redirectUrl.searchParams.set('state', state as string)
  }

  res.redirect(redirectUrl.toString())
}

// Token endpoint for both authorization code and password flows
export const token = async (req: Request, res: Response): Promise<void> => {
  try {
    const { grant_type, client_id, client_secret } = req.body

    if (!grant_type || !client_id) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        error_description: 'Missing required parameters',
      })
      return
    }

    // Validate client
    const client = await oauthClientService.validateClient(
      client_id,
      client_secret
    )
    if (!client) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'invalid_client',
        error_description: 'Invalid client credentials',
      })
      return
    }

    // Validate grant type
    const isValidGrantType = await oauthClientService.validateGrantType(
      client_id,
      grant_type
    )
    if (!isValidGrantType) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'unsupported_grant_type',
        error_description: 'Grant type not supported for this client',
      })
      return
    }

    if (grant_type === 'authorization_code') {
      await handleAuthorizationCodeGrant(req, res)
      return
    } else if (grant_type === 'password') {
      await handlePasswordGrant(req, res)
      return
    } else if (grant_type === 'refresh_token') {
      await handleRefreshTokenGrant(req, res)
      return
    } else {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'unsupported_grant_type',
        error_description: 'Unsupported grant type',
      })
      return
    }
  } catch (error) {
    console.error('Token error:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      error_description: 'Internal server error',
    })
  }
}

// Handle authorization code grant
const handleAuthorizationCodeGrant = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { code, redirect_uri, client_id, code_verifier } = req.body

  if (!code || !redirect_uri) {
    res.status(StatusCodes.BAD_REQUEST).json({
      error: 'invalid_request',
      error_description: 'Missing authorization code or redirect_uri',
    })
    return
  }

  const tokens = await oauthTokenService.exchangeAuthorizationCode(
    code,
    client_id,
    redirect_uri,
    code_verifier
  )

  if (!tokens) {
    res.status(StatusCodes.BAD_REQUEST).json({
      error: 'invalid_grant',
      error_description: 'Invalid authorization code',
    })
    return
  }

  res.json({
    access_token: tokens.access_token,
    token_type: 'Bearer',
    expires_in: tokens.expires_in,
    refresh_token: tokens.refresh_token,
  })
}

// Handle resource owner password credentials grant
const handlePasswordGrant = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { username, password, scope, client_id } = req.body

  if (!username || !password) {
    res.status(StatusCodes.BAD_REQUEST).json({
      error: 'invalid_request',
      error_description: 'Missing username or password',
    })
    return
  }

  const employee = await employeeService.validateEmployee(username, password)
  if (!employee) {
    res.status(StatusCodes.BAD_REQUEST).json({
      error: 'invalid_grant',
      error_description: 'Invalid username or password',
    })
    return
  }

  const tokens = await oauthTokenService.createTokens({
    client_id,
    employee_id: employee.employee_id,
    scope,
  })

  res.json({
    access_token: tokens.access_token,
    token_type: 'Bearer',
    expires_in: tokens.expires_in,
    refresh_token: tokens.refresh_token,
  })
}

// Handle refresh token grant
const handleRefreshTokenGrant = async (
  req: Request,
  res: Response
): Promise<void> => {
  const { refresh_token } = req.body

  if (!refresh_token) {
    res.status(StatusCodes.BAD_REQUEST).json({
      error: 'invalid_request',
      error_description: 'Missing refresh_token',
    })
    return
  }

  const tokens = await oauthTokenService.refreshAccessToken(refresh_token)
  if (!tokens) {
    res.status(StatusCodes.BAD_REQUEST).json({
      error: 'invalid_grant',
      error_description: 'Invalid refresh token',
    })
    return
  }

  res.json({
    access_token: tokens.access_token,
    token_type: 'Bearer',
    expires_in: tokens.expires_in,
  })
}

// Revoke token endpoint
export const revoke = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token, client_id, client_secret, reason } = req.body

    if (!token) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        error_description: 'Missing token',
      })
      return
    }

    // Validate client
    if (!client_id) {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        error_description: 'Missing client_id',
      })
      return
    }

    const client = await oauthClientService.validateClient(
      client_id,
      client_secret
    )
    if (!client) {
      res.status(StatusCodes.UNAUTHORIZED).json({
        error: 'invalid_client',
        error_description: 'Invalid client credentials',
      })
      return
    }

    const success = await oauthTokenService.revokeToken(token, reason)
    if (success) {
      res.status(StatusCodes.OK).json({ message: 'Token revoked successfully' })
    } else {
      res.status(StatusCodes.BAD_REQUEST).json({
        error: 'invalid_request',
        error_description: 'Token not found or already revoked',
      })
    }
  } catch (error) {
    console.error('Revoke error:', error)
    res.status(StatusCodes.INTERNAL_SERVER_ERROR).json({
      error: 'server_error',
      error_description: 'Internal server error',
    })
  }
}
