import { Router } from 'express'
import { authorize, revoke, token } from '../controllers/oauthController'

export const oAuthRouter = Router()

// OAuth 2.0 Authorization endpoint
// GET /oauth/authorize?response_type=code&client_id=...&redirect_uri=...&scope=...&state=...
oAuthRouter.get('/authorize', authorize)
// POST /oauth/authorize (for form submission)
oAuthRouter.post('/authorize', authorize)

// OAuth 2.0 Token endpoint
// POST /oauth/token
oAuthRouter.post('/token', token)

// OAuth 2.0 Token revocation endpoint
// POST /oauth/revoke
oAuthRouter.post('/revoke', revoke)
