
import { useEffect, useState } from 'react'
import { Dashboard } from './components/Dashboard'
import { LoginForm } from './components/LoginForm'
import { oauthService } from './services/oauthService'
import type { AuthState, LoginCredentials } from './types/oauth'

function App() {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    token: null,
    loading: true,
    error: null
  })

  // Check for existing authentication on app load
  useEffect(() => {
    const checkAuth = async () => {
      const token = oauthService.getAccessToken()
      if (token) {
        try {
          // Try to get user info to validate token
          const user = await oauthService.getCurrentUser()
          setAuthState({
            isAuthenticated: true,
            user,
            token,
            loading: false,
            error: null
          })
        } catch (error) {
          // Token is invalid, clear it
          await oauthService.logout()
          setAuthState({
            isAuthenticated: false,
            user: null,
            token: null,
            loading: false,
            error: null
          })
        }
      } else {
        setAuthState(prev => ({ ...prev, loading: false }))
      }
    }

    checkAuth()
  }, [])

  const handleLogin = async (credentials: LoginCredentials) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const tokenResponse = await oauthService.login(credentials)

      // Get user information
      const user = await oauthService.getCurrentUser()

      setAuthState({
        isAuthenticated: true,
        user,
        token: tokenResponse.access_token,
        loading: false,
        error: null
      })
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Login failed'
      }))
    }
  }

  const handleLogout = async () => {
    setAuthState(prev => ({ ...prev, loading: true }))

    try {
      await oauthService.logout()
      setAuthState({
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: null
      })
    } catch (error) {
      console.error('Logout error:', error)
      // Force logout even if server call fails
      setAuthState({
        isAuthenticated: false,
        user: null,
        token: null,
        loading: false,
        error: null
      })
    }
  }

  // Show loading spinner while checking authentication
  if (authState.loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex items-center space-x-2">
          <svg className="animate-spin h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span className="text-lg text-gray-600">Loading VarPro Admin...</span>
        </div>
      </div>
    )
  }

  // Show login form if not authenticated
  if (!authState.isAuthenticated) {
    return (
      <LoginForm
        onLogin={handleLogin}
        loading={authState.loading}
        error={authState.error}
      />
    )
  }

  // Show dashboard if authenticated
  return (
    <Dashboard
      user={authState.user}
      token={authState.token}
      onLogout={handleLogout}
    />
  )
}

export default App
